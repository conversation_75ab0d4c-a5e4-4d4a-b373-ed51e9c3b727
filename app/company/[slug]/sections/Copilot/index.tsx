'use client';
import {
  Fragment,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Transition } from '@headlessui/react';
import { motion } from 'framer-motion';
import {
  AIButton, Button, 
} from '@/common/components/atoms';
import {
  AIIcon,
  ThreeStarIcon,
} from '@/common/components/icons';
import {
  mixpanelCustomEvent, MixpanelEventName,
} from "@/common/utils/mixpanel/eventTriggers";
import lang from '@/common/lang';
import { CopilotPrompts } from './copilotPrompts';
import { Navigation } from './navigation';
import { CopilotType } from './types';
import './style.css'
import { useWindowDimensions } from '@/common/hooks';

const { showcase: { copilot: copilotCopy } } = lang

const Copilot = ({
  companyName,
  heroCTALabel,
  heroCTALink,
  slug,
  copilotOpened,
  setCopilotOpened,
  prompts,
}: CopilotType) => {
  const [copilotExpanded, setCopilotExpanded] = useState(false);
  const [showBoxShadow, setShowBoxShadow] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({
    x: 0,
    y: 0, 
  });
  const {
    windowDimensions: {
      width, height, 
    },
  } = useWindowDimensions();
  const [constraints, setConstraints] = useState({
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  });
  const constraintsRef = useRef(null);

  useEffect(() => {
    const buttonWidth = 200;
    const buttonHeight = 48;
    
    setConstraints({
      left: -(width / 2),
      right: (width / 2) - buttonWidth,
      top: -(height - buttonHeight - 32),
      bottom: 0,
    });
  }, [width, height]);

  if (!copilotOpened) {
    return (<motion.div 
      ref={constraintsRef}
      className="fixed left-1/2 -translate-x-1/2 bottom-4 md:bottom-8 z-[49]"
      drag
      dragConstraints={constraints}
      dragElastic={0}
      dragMomentum={false}
      onDragStart={() => {
        setIsDragging(true);
      }}
      onDragEnd={(event, info) => {
        event.stopPropagation();
        setPosition({
          x: position.x + info.offset.x,
          y: position.y + info.offset.y,
        });
        setIsDragging(false);
      }}
      style={{
        x: position.x,
        y: position.y,
      }}
    >
      <AIButton
        onClick={() => { 
          if (!isDragging)
          { setCopilotOpened(true) }
        }
        }
        containerClassName={`rounded-full ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
        as="button"
        className="bg-transparent text-white flex items-center space-x-2"
      >
        <AIIcon id="showcase-ai-icon" />
        <span className="whitespace-nowrap"> {copilotCopy.curationAi}</span>
      </AIButton>
    </motion.div>)
  }

  return (
    <Transition
      show={copilotOpened}
      as={Fragment}
      enter="transition-opacity ease-in delay-200 duration-300"
      enterFrom="opacity-0"
      enterTo="opacity-100"
      leave="transition-opacity ease-out duration-200"
      leaveFrom="opacity-100"
      leaveTo="opacity-0"
    >
      <div className="fixed left-1/2 -translate-x-1/2 bottom-4 md:bottom-8 z-[49]">
        <div className={`w-[calc(100vw-38px)] flex flex-col duration-1000 ease-in-out bg-[#0E0E0F] ${showBoxShadow ? "box-glow" : "box-border shadow-copilot"} box rounded-lg p-2 ${copilotExpanded && copilotOpened ? "h-[85vh] lg:w-[60vw]" : " lg:w-[820px] max-h-[85vh]"}`}>
          <Transition
            show={!copilotOpened}
            as="div"
            enter="transition-opacity ease-in delay-200 duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-out duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            {!copilotOpened && (
              <div className='flex justify-between items-center'>
                <Navigation
                  slug={slug}
                  heroCTALabel={heroCTALabel}
                  heroCTALink={heroCTALink}
                />
                <Button
                  type="button"
                  variant="secondaryOutlined"
                  onClick={() => {
                    setCopilotOpened(!copilotOpened)
                    mixpanelCustomEvent({
                      eventName: MixpanelEventName.copilotOpened,
                      mixpanelProps: {
                        page: 'Showcase',
                        slug: slug,
                      },
                    })
                  }}
                  size="sm"
                  className="py-2.5 px-4 md:px-6 md:py-2.5 font-semibold !text-base text-white"
                >
                  <ThreeStarIcon />
                  {copilotCopy.curationAi}
                </Button>
              </div>
            )}
          </Transition>
          <Transition
            show={copilotOpened}
            as="div"
            enter="transition-opacity ease-in delay-200 duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            className="flex-1 flex flex-col min-h-0"
            leave="transition-opacity ease-out duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            {copilotOpened && (
              <CopilotPrompts
                companyName={companyName}
                setCopilotOpened={setCopilotOpened}
                copilotExpanded={copilotExpanded}
                setCopilotExpanded={setCopilotExpanded}
                slug={slug}
                setShowBoxShadow={setShowBoxShadow}
                aiPrompts={prompts}
              />
            )}
          </Transition>
        </div>
      </div>
    </Transition>
  )
}

export default Copilot;
