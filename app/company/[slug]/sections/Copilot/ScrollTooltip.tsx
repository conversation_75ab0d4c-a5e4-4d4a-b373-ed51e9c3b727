import { motion } from 'framer-motion';
import {
  useEffect, useState, 
} from 'react';

export const ScrollTooltip = () => {
  const [position, setPosition] = useState({
    x: 0,
    y: 0, 
  });

  const updatePosition = (e: MouseEvent) => {
    const target = document.querySelector('.carousel-container') as HTMLElement;
    const rect = target.getBoundingClientRect();
    setPosition({
      x: e.clientX -  rect.left - 20,
      y: e.clientY - rect.top - 10,
    });
  };

  useEffect(() => {
    document.addEventListener('mousemove', updatePosition);
    return () => document.removeEventListener('mousemove', updatePosition);
  }, []);

  return (
    <motion.div
      className="absolute pointer-events-none z-50 hidden md:block"
      animate={{
        x: position.x,
        y: position.y, 
      }}
      initial={false}
      transition={{ duration: 0 }}
    >
      <div className="bg-black/40 backdrop-blur-sm px-2 py-1 rounded-xl flex items-center gap-3 text-sm text-white whitespace-nowrap">
        <span>❮</span>
        <span>❯</span>
      </div>
    </motion.div>
  );
};