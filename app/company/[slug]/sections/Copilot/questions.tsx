import { Question } from "@/common/components/atoms"
import Carousel from "@/common/components/organisms/carouselSwiper";
import {
  mixpanelCustomEvent, MixpanelEventName,
} from "@/common/utils/mixpanel/eventTriggers";
import lang from "@/common/lang"
import { QuestionsProps } from "./types"
import { ArrowRight } from "lucide-react";
const { showcase: { copilot: copilotCopy } } = lang
import { ScrollTooltip } from './ScrollTooltip';
import { useState } from 'react';
import { isCustomPromptsDisabled } from "@/common/constants";

export const Questions = ({
  questionsExpanded,
  flagForRefetching,
  onSendPrompt,
  slug,
  aiPrompts,
  toggleCustomEnable,
  promptExpanded = false,
}: QuestionsProps) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const handleQuestion = async (text: string) => {
    await onSendPrompt(text)
    mixpanelCustomEvent({
      eventName: MixpanelEventName.questionForPrompt,
      mixpanelProps: {
        page: 'Showcase',
        slug: slug,
        question: text,
      },
    })
  }

  const questions = aiPrompts || [];

  return (
    <div className="mb-4 mt-1">
      {questionsExpanded ? (
        <div className="flex gap-2 flex-wrap">
          {questions.map((question, index) => (
            <Question
              disabled={flagForRefetching}
              key={index}
              text={question.question}
              handleClick={handleQuestion}
            />
          ))}
          <button disabled={flagForRefetching} onClick={toggleCustomEnable} className={`py-2 px-4 lg:py-[6px] text-sm lg:text-base text-center leading-none bg-white text-[#3F3D42] hover:bg-white/85 rounded-md transition-all whitespace-nowrap ml-4`} type="button">
            {!promptExpanded ? <div className="flex gap-2 items-center">{copilotCopy.addCustomPrompt} <ArrowRight height={18} width={18} /></div> : copilotCopy.closeCustomPrompt}
          </button>
        </div>
      ) : (
        <div 
          className="carousel-container"
          onMouseEnter={() => setShowTooltip(true)}
          onMouseMove={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
        >
          <Carousel items={
            questions.map((question, index) => (
              <>
                <Question
                  key={index}
                  disabled={flagForRefetching}
                  text={question.question}
                  handleClick={handleQuestion}
                />
                {index === questions.length - 1 && <button disabled={flagForRefetching} onClick={toggleCustomEnable} className={`py-2 px-4 lg:py-[6px] text-sm lg:text-base text-center leading-none bg-white text-[#3F3D42] hover:bg-white/85 rounded-md transition-all whitespace-nowrap ml-4`} type="button">
                  {!promptExpanded ? <div className="flex gap-2 items-center">{copilotCopy.addCustomPrompt} <ArrowRight height={18} width={18} /></div> : copilotCopy.closeCustomPrompt}
                </button>}
              </>
            ))
          } />
          {showTooltip && !isCustomPromptsDisabled && <ScrollTooltip />}
        </div>
      )}
    </div>)
}
