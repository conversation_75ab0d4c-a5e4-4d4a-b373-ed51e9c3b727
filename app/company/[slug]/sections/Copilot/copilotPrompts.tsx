import {
  useEffect,
  useRef,
  useState,
} from "react"
import {
  But<PERSON>,
} from "@/common/components/atoms"
import {
  AIIcon,
  CopilotCancelIcon,
  CopilotCollapseIcon,
  CopilotExpandIcon,
  QuestionIcon,
} from "@/common/components/icons"
import {
  mixpanelCustomEvent, MixpanelEventName,
} from "@/common/utils/mixpanel/eventTriggers";
import lang from "@/common/lang"
import { copilotAssistantRole } from "@/common/constants"
import { Prompt } from "./prompt"
import { useSendPrompt } from "../../hooks/useSendPrompt";
import { Questions } from "./questions"
import { useGetPrompts } from "../../hooks/useGetPrompts"
import { Prompts } from "./prompts"
import { CopilotPromptsType } from "./types"
import { PromptDTO } from "../../types"
import toast from "react-hot-toast"
import { useWindowDimensions } from "@/common/hooks";

const TIMEOUT_FOR_REFETCH = 40000

const { showcase: { copilot: copilotCopy } } = lang

export const CopilotPrompts = ({
  setCopilotOpened,
  setCopilotExpanded,
  copilotExpanded,
  companyName,
  setShowBoxShadow,
  slug,
  aiPrompts,
}: CopilotPromptsType) => {
  const isNewSession = sessionStorage.getItem('threadId') === null;
  const [questionsExpanded, setQuestionsExpanded] = useState(false)
  const [promptExpanded, setPromptExpanded] = useState(false)
  const [flagForRefetching, setFlagForRefetching] = useState(false)
  const [enableTypewriter, setEnableTypewriter] = useState(false);
  const [newPrompt, setNewPrompt] = useState<PromptDTO | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const {
    isDesktopView,
  } = useWindowDimensions()
  const {
    mutatePrompts,
    isPromptsLoading,
    prompts,
  } = useGetPrompts({
    slug,
    flagForRefetching,
    isNewSession,
  })


  useEffect(() => {
    if (prompts && flagForRefetching) {
      const latestAssistantMessage = prompts[prompts.length - 1]
      if (latestAssistantMessage?.role === copilotAssistantRole && latestAssistantMessage?.content) {
        setFlagForRefetching(false)
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        if (enableTypewriter) {
          setNewPrompt(prompts[prompts.length - 1]);
        }
      }
    }
  }, [prompts, flagForRefetching, enableTypewriter])

  useEffect(() => {
    if (flagForRefetching) {
      timeoutRef.current = setTimeout(() => {
        setFlagForRefetching(false)
        toast.error(copilotCopy.promptTimeoutError)
      }, TIMEOUT_FOR_REFETCH)
    }
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [flagForRefetching])

  const {
    onSendPrompt,
    isPrompting,
  } = useSendPrompt({
    flagForRefetching,
    slug,
    mutatePrompts,
    prompts,
    setFlagForRefetching,
    setQuestionsExpanded,
    setEnableTypewriter,
  });

  useEffect(() => {
    if (isPromptsLoading || isPrompting) {
      setShowBoxShadow(true)
      return
    }
    setShowBoxShadow(false)
  }, [isPromptsLoading, isPrompting, setShowBoxShadow])

  const handleClose = () => {
    setCopilotOpened(false)
    mixpanelCustomEvent({
      eventName: MixpanelEventName.copilotClosed,
      mixpanelProps: {
        page: 'Showcase',
        slug: slug,
      },
    })
  }

  const toggleQuestions = () => {
    setQuestionsExpanded(!questionsExpanded)
    if (!questionsExpanded) {
      mixpanelCustomEvent({
        eventName: MixpanelEventName.questionsOpened,
        mixpanelProps: {
          page: 'Showcase',
          slug: slug,
        },
      })
    }
  }


  return (
    <div className="pl-2 md:px-2 min-h-0 flex flex-col flex-1">
      <div className="flex justify-between">
        <div className="text-[#f5f5f5] font-semibold flex items-center gap-2">
          <AIIcon id="copilot" />
          {!isDesktopView ? copilotCopy.curationAi : copilotCopy.surfaceInsights}
        </div>
        <div className="flex gap-1 items-center">
          <Button
            type="button"
            variant="secondaryOutlined"
            size="sm"
            className={`!p-1 md:!p-2.5 ${questionsExpanded ? "text-[#D6D3DB]" : "text-[#BDB7C7]"} hover:text-[#e5e5e5]`}
            onClick={toggleQuestions}
          >
            <QuestionIcon className="md:w-4 md:h-4" />
            <span className="hidden md:inline">{copilotCopy.questions}</span>
          </Button>
          <Button
            type="button"
            variant="secondaryOutlined"
            onClick={() => setCopilotExpanded(!copilotExpanded)}
            size="sm"
            className="!p-1 text-[#BDB7C7] hover:text-[#e5e5e5] hidden md:block"
          >
            {copilotExpanded ? <CopilotCollapseIcon /> : <CopilotExpandIcon />}
          </Button>
          <Button
            type="button"
            variant="secondaryOutlined"
            onClick={handleClose}
            size="sm"
            className="!p-1 text-[#BDB7C7] hover:text-[#e5e5e5]"
          >
            <CopilotCancelIcon />
          </Button>
        </div>
      </div>
      <div className="mt-4 pr-2 md:pr-0 min-h-0 flex flex-col flex-1">
        <div>
          {isNewSession && copilotExpanded && <div className="text-white font-semibold">
            {copilotCopy.heading.replace("%company%", companyName)}
          </div>}
          {isNewSession && copilotExpanded && <div className="text-[#d4d4d4] mt-1 mb-4">
            {copilotCopy.description}
          </div>}
        </div>
        <div className='overflow-y-auto flex-1 flex flex-col'>
          <Prompts
            slug={slug}
            aiPrompts={aiPrompts}
            flagForRefetching={flagForRefetching}
            prompts={prompts}
            copilotExpanded={copilotExpanded}
            questionsExpanded={questionsExpanded}
            onSendPrompt={onSendPrompt}
            isNewSession={isNewSession}
            isPromptsLoading={isPromptsLoading}
            isPrompting={isPrompting}
            enableTypewriter={enableTypewriter}
            setEnableTypewriter={setEnableTypewriter}
            newPrompt={newPrompt}
            setNewPrompt={setNewPrompt}
            promptExpanded={promptExpanded}
            toggleCustomEnable={() => { setPromptExpanded(!promptExpanded) }}
            isDesktopView={isDesktopView}
          />
        </div>
        {isDesktopView && <div className={`pr-2 md:pr-0 min-h-0 mobile-hide flex flex-col ${questionsExpanded && !promptExpanded && 'mb-5'}`}>
          <Questions
            slug={slug}
            flagForRefetching={flagForRefetching}
            questionsExpanded={questionsExpanded}
            copilotExpanded={copilotExpanded}
            onSendPrompt={onSendPrompt}
            aiPrompts={aiPrompts}
            promptExpanded={promptExpanded}
            toggleCustomEnable={() => { setPromptExpanded(!promptExpanded) }}
          />
        </div>}
        {promptExpanded && <Prompt
          slug={slug}
          onSendPrompt={onSendPrompt}
          isPrompting={isPrompting}
          flagForRefetching={flagForRefetching}
        />}
        <div className="md:hidden text-[#a3a3a3] text-xs text-right mb-2" dangerouslySetInnerHTML={{ __html: copilotCopy.disclaimerMobile }} />
        {(copilotExpanded || promptExpanded) && <div className="hidden md:block text-[#a3a3a3] text-xs text-center mb-1">
          {copilotCopy.disclaimer}
        </div>}
      </div>
    </div>
  )
}
