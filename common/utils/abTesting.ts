import {
  mixpanelCustomEvent,
  MixpanelEventName,
} from "@/common/utils/mixpanel/eventTriggers";
import { ButtonDistribution } from "@/common/hooks/useButtonDistribution";

export type ButtonType = "watchlist" | "rate_showcase";

export interface ABTestResult {
  buttonType: ButtonType;
  shouldShowRateButton: boolean;
}

export const determineButtonDisplay = (
  distribution: ButtonDistribution,
  slug: string,
  userId?: string
): ABTestResult => {
  try {
    let watchlistProbability = 0.5;

    if (distribution.total > 10) {
      const watchlistPercentage = distribution.watchlistPercentage;

      if (watchlistPercentage > 55) {
        watchlistProbability = 0.3;
      } else if (watchlistPercentage < 45) {
        watchlistProbability = 0.7;
      }
    }

    const seed = userId || getSessionSeed();
    const random = seededRandom(seed + slug);

    const showWatchlist = random < watchlistProbability;
    const buttonType: ButtonType = showWatchlist
      ? "watchlist"
      : "rate_showcase";

    trackButtonDisplay(buttonType, slug, distribution);

    return {
      buttonType,
      shouldShowRateButton: !showWatchlist,
    };
  } catch (error) {
    console.error("Error in determineButtonDisplay:", error);

    const fallbackRandom = Math.random();
    const buttonType: ButtonType =
      fallbackRandom < 0.5 ? "watchlist" : "rate_showcase";

    trackButtonDisplay(buttonType, slug, distribution);

    return {
      buttonType,
      shouldShowRateButton: buttonType === "rate_showcase",
    };
  }
};

const trackButtonDisplay = (
  buttonType: ButtonType,
  slug: string,
  distribution: ButtonDistribution
) => {
  if (typeof window === "undefined") {
    return;
  }

  const trackingKey = `button-display-tracked-${slug}`;

  const alreadyTracked = localStorage.getItem(trackingKey);

  if (alreadyTracked) {
    return;
  }

  mixpanelCustomEvent({
    eventName: MixpanelEventName.buttonDisplayed,
    mixpanelProps: {
      page: "Showcase",
      slug: slug,
      buttonType: buttonType,
      currentWatchlistPercentage: distribution.watchlistPercentage,
      currentRateShowcasePercentage: distribution.rateShowcasePercentage,
      totalEvents: distribution.total,
    },
  });

  localStorage.setItem(trackingKey, "true");
};

const getSessionSeed = (): string => {
  if (typeof window === "undefined") {
    return "server-fallback";
  }

  let seed = localStorage.getItem("ab-test-seed");
  if (!seed) {
    seed = Math.random().toString(36).substring(2, 15);
    localStorage.setItem("ab-test-seed", seed);
  }
  return seed;
};

const seededRandom = (seed: string): number => {
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash;
  }

  const normalized = Math.abs(hash) / 2147483647;
  return normalized;
};

export const shouldShowBothButtons = (isAuthenticated: boolean): boolean => {
  return isAuthenticated;
};
